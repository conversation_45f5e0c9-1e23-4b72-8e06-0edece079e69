import { IOwnerInfo, IUnifiedApplicationDraft } from '@linqpal/models'
import { decrypt } from '../linqpal/decisionEngine/wrapper'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import { Draft, LexisService } from '@linqpal/common-backend'
import md5 from 'crypto-js/md5'
import { States, statesHashMap } from '@linqpal/models/src/dictionaries'
import {
  IAccountInfo,
  IBusinessInstantID,
} from '@linqpal/common-backend/src/models/lexis-nexis.model/types'
import moment from 'moment/moment'

type IBusinessInstantIDRequest = {
  ownerId?: string
  body: IBusinessInstantID
  isPrincipal?: boolean
  key: string
}

export async function getApplicationKYBData(
  draft: IUnifiedApplicationDraft,
  companyId: string,
) {
  const principalEin = await decrypt(draft.businessInfo_ein)

  const coOwners = draft.owners?.length
    ? draft.owners
    : await getLegacyCoOwners(companyId)

  const individualOwners = coOwners.filter(
    (owner) => owner.type === OwnerTypes.INDIVIDUAL,
  )
  const ownerEntities = coOwners.filter(
    (owner) => owner.type === OwnerTypes.ENTITY,
  )

  const requests: IBusinessInstantIDRequest[] = await Promise.all([
    createPrincipalBusinessRequest(draft, individualOwners, principalEin),
    ...ownerEntities.map(async (entity) =>
      createOwnerEntityRequest(entity, principalEin),
    ),
  ])

  const responses = await Promise.all(
    requests.map((request) =>
      LexisService.businessInstantId(request.key, request.body).then(
        (response) => ({
          owner: {
            id: request.ownerId,
            businessName: request.body.businessName,
            isPrincipal: request.isPrincipal,
            type: OwnerTypes.ENTITY,
            key: md5(request.key).toString(),
          },
          BVI: response.BVI,
          BRI: response.BRI,
          KYC: response.owners,
        }),
      ),
    ),
  ).catch((e) => e.message)

  if (Array.isArray(responses)) {
    // remove KYC scores for authorized reps of entities, not interested in this data
    return {
      KYB: responses.map((data) => ({
        owner: data.owner,
        BVI: data.BVI,
        BRI: data.BRI,
      })),
      KYC: responses[0]?.KYC,
    }
  } else {
    return { error: responses }
  }
}

async function createPrincipalBusinessRequest(
  draft: IUnifiedApplicationDraft,
  coOwners: IOwnerInfo[],
  ein: string,
): Promise<IBusinessInstantIDRequest> {
  const accounts = await Promise.all([
    fillOwnerAccount(draft),
    ...coOwners.map(async (owner) => fillCoOwnerAccount(owner)),
  ])

  return {
    body: {
      businessName:
        draft.businessInfo_businessName.legalName ||
        draft.businessInfo_businessName.dba,
      businessFein: ein,
      businessTelephoneNumber: draft.businessInfo_businessPhone,
      businessAddressZip: draft.businessInfo_businessAddress?.zip,
      businessAddressStreet1: draft.businessInfo_businessAddress?.address,
      businessAddressState:
        statesHashMap[draft.businessInfo_businessAddress?.state as States],
      businessAddressCity: draft.businessInfo_businessAddress?.city,
      accounts,
    },
    ownerId: undefined,
    isPrincipal: true,
    key: ein,
  }
}

async function createOwnerEntityRequest(
  owner: IOwnerInfo,
  principalEin: string,
): Promise<IBusinessInstantIDRequest> {
  const ein = await decrypt(owner.ein)

  return {
    body: {
      businessName: owner.entityName,
      businessFein: ein,
      businessTelephoneNumber: owner.phone,
      businessAddressZip: owner.zip,
      businessAddressStreet1: owner.address,
      businessAddressState: statesHashMap[owner.state as States],
      businessAddressCity: owner.city,
      accounts: [
        {
          // for entities first / last name means name of an authorized representative
          firstName: owner.firstName,
          lastName: owner.lastName,
        },
      ],
    },
    ownerId: owner.id,
    // use composite key to prevent overwriting business which is registered in BlueTape
    // with same business which just owns a business registered in BlueTape
    key: ein?.concat(principalEin),
  }
}

async function fillOwnerAccount(
  draft: IUnifiedApplicationDraft,
): Promise<IAccountInfo> {
  const authorizedSSN = await decrypt(draft.businessOwner_ssn)
  return {
    id: draft.businessOwner_id,
    lastName: draft.businessOwner_lastName,
    firstName: draft.businessOwner_firstName,
    street: draft.businessOwner_address?.address,
    city: draft.businessOwner_address?.city,
    state: statesHashMap[draft.businessOwner_address?.state as States],
    zip: draft.businessOwner_address?.zip,
    ssn: authorizedSSN,
    phone: draft.businessOwner_phone,
    email: draft.businessOwner_email,
    birthday: moment(draft.businessOwner_birthdate, 'MM/DD/YYYY').format(
      'YYYYMMDD',
    ),
    isPrincipal: true,
  }
}

async function fillCoOwnerAccount(owner: IOwnerInfo): Promise<IAccountInfo> {
  const ssn = await decrypt(owner.ssn)
  return {
    id: owner.id,
    lastName: owner.lastName,
    firstName: owner.firstName,
    phone: owner.phone,
    email: owner.email,
    ssn,
    street: owner.address,
    city: owner.city,
    state: statesHashMap[owner.state as States],
    zip: owner.zip,
    birthday: moment(owner.birthday, 'MM/DD/YYYY').format('YYYYMMDD'),
  }
}

async function getLegacyCoOwners(companyId: string): Promise<IOwnerInfo[]> {
  const doc = await Draft.findOne({
    company_id: companyId,
    type: 'loan_application',
  })

  return (
    doc?.data
      .get('coOwnerInfo')
      ?.items.filter((item) => item.content?.percentOwned > 0) || []
  ).map((item) => item.content as IOwnerInfo)
}
