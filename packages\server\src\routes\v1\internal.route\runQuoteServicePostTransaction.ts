import { getApiURL, GlobalLogger } from '@linqpal/common-backend'
import { QuoteService } from '@linqpal/common-backend/src/services/quote/quote.service'
import { apiKeyRequired } from '../../../services/auth.service'
import { ControllerItem } from '../../controllerItem'

interface IRunQuoteServicePostTransaction {
  quoteId: string
  amount: number
}

export const runQuoteServicePostTransaction = {
  middlewares: { pre: [apiKeyRequired()] },
  async post(req, res) {
    if (!isBodyValid(req.body)) {
      throw new Error('Body is invalid')
    }

    GlobalLogger.info(
      {
        quoteId: req.body.quoteId,
        amount: req.body.amount,
      },
      `BO user posting transaction for quote ${req.body.quoteId}`,
    )

    try {
      await QuoteService.postTransaction(
        req.body.quoteId,
        req.body.amount,
        getApiURL(),
        req.session,
      )
    } catch (err) {
      GlobalLogger.error(
        { err },
        "The error occurred during 'QuoteService.postTransaction' execution in 'runQuoteServicePostTransaction'",
      )

      throw err
    }

    res.send({ result: 'ok' })
  },
} as ControllerItem

function isBodyValid(value: any): value is IRunQuoteServicePostTransaction {
  if (typeof value !== 'object') {
    return false
  }

  if (value === null) {
    return false
  }

  if (Array.isArray(value)) {
    return false
  }

  if (typeof value.quoteId !== 'string') {
    return false
  }

  if (typeof value.amount !== 'number') {
    return false
  }

  if (!Number.isFinite(value.amount)) {
    return false
  }

  return true
}
