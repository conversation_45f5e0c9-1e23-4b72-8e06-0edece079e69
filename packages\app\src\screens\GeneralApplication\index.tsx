import React from 'react'
import { getFocusedRouteNameFromRoute } from '@react-navigation/core'
import { createStackNavigator } from '@react-navigation/stack'
import { screenOptions } from '../../utils/helpers/commonUtils'
import Application from './Application'

const Stack = createStackNavigator()

export default ({ navigation, route }) => {
  React.useLayoutEffect(() => {
    let tabBarVisible
    const routeName = getFocusedRouteNameFromRoute(route)
    if (typeof routeName === 'string') {
      tabBarVisible = !['RouteNameToHideTabBar'].includes(routeName)
    }
    navigation.setOptions({ tabBarVisible })
  }, [navigation, route])

  return (
    <Stack.Navigator screenOptions={screenOptions}>
      <Stack.Screen name={'Home'} component={Application} />
    </Stack.Navigator>
  )
}
