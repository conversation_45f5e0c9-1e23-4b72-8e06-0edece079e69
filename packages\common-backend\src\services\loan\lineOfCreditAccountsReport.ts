import { Company, Invoice, LoanApplication } from '../../models'
import {
  ICompany,
  IInvoice,
  ILoanApplication,
  ILoanPaymentPlan,
  ILoanPricingPackage,
} from '../../models/types'
import { Logger } from '../logger/logger.service'
import { readKnockout } from './knockoutReader'
import { IUnifiedApplicationDraft, OutputData } from '@linqpal/models'
import { readDraft } from '@linqpal/models/src/helpers/draftReader'
import moment from 'moment-timezone'
import round from 'lodash/round'
import sum from 'lodash/sum'
import {
  ILoan,
  ReceivableStatus,
  ReceivableType,
  ScheduleStatus,
} from '../lms.service'
import { LOAN_APPLICATION_STATUS } from '@linqpal/models/src/dictionaries'
import {
  abbreviateState,
  formatDate,
  formatNumericScore,
  getAverageMonthlyCashFlow,
  getDrawApprovals,
  getInterchangeOnVC,
  getLoans,
  getLoanType,
  getPaymentPlan,
  getPaymentPlans,
  getPricingPackage,
  getPricingPackages,
  getReceivablesCollectedTotal,
  getReceivablesOutstandingTotal,
  getSixMonthAverageBalance,
  mapCoOwnersKnockouts,
  yesForRejected,
} from './utils'
import { CompanyUtils } from '@linqpal/models/src/helpers/companyUtils'
import { ReportFormatter } from './reportFormatter'
import { IDrawApproval } from '../onBoarding/types'
import { AgreementType } from '../agreement/types'
import { DocumentVersioningService } from '../../../index'

const LOC_START_DATE = '2023-07-07'

interface ILmsLoanApplication extends ILoanApplication {
  invoice: IInvoice
  customer: ICompany
  supplier: ICompany
  lmsLoan: ILoan | null
  drawApproval: IDrawApproval | null
}

interface ICompanyLoans extends ICompany {
  applications: ILmsLoanApplication[]
}

export default async function lineOfCreditAccountsReport(logger: Logger) {
  // use $project to reduce amount of data as much as possible to keep memory low on lambda
  const pipeline = [
    {
      $lookup: {
        from: LoanApplication.collection.name,
        let: { id: { $toString: '$_id' } },
        as: 'applications',
        pipeline: [
          {
            $match: {
              $and: [
                { $expr: { $eq: ['$company_id', '$$id'] } },
                { lms_id: { $exists: true } },
                { $expr: { $ne: ['$lms_id', null] } },
                {
                  status: {
                    $in: [
                      LOAN_APPLICATION_STATUS.APPROVED,
                      LOAN_APPLICATION_STATUS.CLOSED,
                    ],
                  },
                },
              ],
            },
          },
          {
            $lookup: {
              from: Invoice.collection.name,
              as: 'invoice',
              let: {
                invoiceId: {
                  $convert: {
                    input: {
                      $cond: {
                        if: {
                          $eq: [
                            { $type: '$invoiceDetails.invoiceId' },
                            'array',
                          ],
                        },
                        then: { $first: '$invoiceDetails.invoiceId' },
                        else: '$invoiceDetails.invoiceId',
                      },
                    },
                    to: 'objectId',
                    onError: null,
                  },
                },
              },
              pipeline: [
                { $match: { $expr: { $eq: ['$_id', '$$invoiceId'] } } },
                {
                  $project: {
                    company_id: 1,
                    project_id: 1,
                  },
                },
              ],
            },
          },
          {
            $unwind: {
              path: '$invoice',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $lookup: {
              from: Company.collection.name,
              as: 'supplier',
              let: {
                supplierId: {
                  $convert: {
                    input: '$invoice.company_id',
                    to: 'objectId',
                    onError: null,
                  },
                },
              },
              pipeline: [
                { $match: { $expr: { $eq: ['$_id', '$$supplierId'] } } },
                {
                  $project: {
                    name: 1,
                    legalName: 1,
                    'settings.loanPricingPackageId': 1,
                  },
                },
              ],
            },
          },
          {
            $unwind: {
              path: '$supplier',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $project: {
              // There are test companies with large number of bank accounts & loan applications,
              // which result in huge amount of errors in Giact step output, so size of joined doc exceeds Mongo limits.
              // So take only necessary output steps
              company_id: 1,
              issueDate: 1,
              approvedAmount: 1,
              draft: 1,
              invoiceDetails: 1,
              metadata: 1,
              lms_id: 1,
              outputs: {
                $filter: {
                  input: '$outputs',
                  as: 'output',
                  cond: {
                    $in: [
                      '$$output.step',
                      [
                        'Knockout',
                        'creditStatus',
                        'LoanDecision',
                        'ProcessManualData',
                        'ProcessFinicityData',
                        'ProcessPlaidData',
                      ],
                    ],
                  },
                },
              },
              invoice: 1,
              supplier: 1,
              settings: 1,
            },
          },
        ],
      },
    },
    { $match: { 'applications.0': { $exists: true } } },
  ]

  const companies = await Company.aggregate(pipeline)
    .sort({ createdAt: -1 })
    .allowDiskUse(true)

  logger.info(`found ${companies?.length} companies`)

  const apps = companies.flatMap((c) => c.applications)

  const [
    loans,
    drawApprovals,
    paymentPlans,
    pricingPackages,
    latestAgreements,
  ] = await Promise.all([
    getLoans(apps),
    getDrawApprovals(apps),
    getPaymentPlans(apps),
    getPricingPackages(apps),
    getLatestAgreements(companies),
  ])

  logger.info(`found ${loans.length} loans`)
  logger.info(
    `pre-fetched ${paymentPlans?.size} missed payment plans and ${pricingPackages?.size} missed pricing packages`,
  )

  const reportLines = await Promise.all(
    companies.flatMap((company) => {
      company.applications.forEach((app: ILmsLoanApplication) => {
        app.lmsLoan = loans.find((loan) => loan.id === app.lms_id) ?? null
        app.drawApproval =
          drawApprovals.find(
            (approval) => approval.id === app.drawApprovalId,
          ) ?? null
      })

      const loCCompany = {
        ...company,
        applications: company.applications.filter((app: ILoanApplication) =>
          moment(app.issueDate).isAfter(moment(LOC_START_DATE), 'date'),
        ),
      }

      const preLoCCompany = {
        ...company,
        applications: company.applications.filter((app: ILoanApplication) =>
          moment(app.issueDate).isSameOrBefore(moment(LOC_START_DATE), 'date'),
        ),
      }

      return [
        mapReport(loCCompany, paymentPlans, pricingPackages, latestAgreements),
        mapReport(
          preLoCCompany,
          paymentPlans,
          pricingPackages,
          latestAgreements,
          true,
        ),
      ]
    }),
  )

  return reportLines.filter((line) => line)
}

export async function mapReport(
  company: ICompanyLoans,
  paymentPlans: Map<string, ILoanPaymentPlan | null>,
  pricingPackages: Map<string, ILoanPricingPackage | null>,
  latestAgreements: Map<string, string>,
  preLoC = false,
) {
  const firstApp = company.applications[0]
  if (!firstApp) return

  const firstDraft = readDraft(firstApp.draft)
  const lastApp = company.applications[company.applications.length - 1]

  const firstKnockout = readKnockout(firstApp)
  const lastKnockout = readKnockout(lastApp)

  const firstCreditStatus =
    firstApp.outputs?.find((o) => o.step === 'creditStatus')?.data || {}
  const lastCreditStatus =
    lastApp.outputs?.find((o) => o.step === 'creditStatus')?.data || {}

  const firstLoanDecision =
    firstApp.outputs?.find((o) => o.step === 'LoanDecision')?.data || {}
  const lastLoanDecision =
    lastApp.outputs?.find((o) => o.step === 'LoanDecision')?.data || {}

  const firstCoOwnersReport = mapCoOwnersKnockouts(firstKnockout)
  const lastCoOwnersReport = mapCoOwnersKnockouts(lastKnockout)

  const paymentsSummary = getPaymentsSummary(company)
  const revenue = await getRevenue(company, paymentPlans, pricingPackages)
  const outstandingFees = getOutstandingFees(company)

  const holdAmount = company.applications.reduce(
    (total, app) => total + (app.drawApproval?.creditHoldAmount ?? 0),
    0,
  )

  // prettier-ignore
  return {
    'Customer': company.legalName || company.name || '',
    'CustomerID': firstApp.company_id,
    'Customer Category Code': '', // TODO: VK: Clarify
    'LOC ID': preLoC ? 'pre-LoC' : company.credit?.LoCnumber?.toString() ?? '',
    'LOC Creation Date': preLoC
      ? ''
      : formatDate(company.credit?.LoCnumCreatedAt),
    // TODO: VK: remove as any when deposit details is in prod
    'LOC Security Deposit': (company.settings as any)?.depositDetails?.isSecured
      ? ReportFormatter.addCurrencyCell((company.settings as any)?.depositDetails?.depositAmount ?? 0)
      : ReportFormatter.addCurrencyCell(undefined, ''), // TODO: VK: remove as any when down payment is done
    'Account Agreement Version': latestAgreements.get(company._id.toString()) ?? '',
    'Account Status': '', // placeholder
    'Account Tier': '', // placeholder
    'Supplier ID (if sponsored by a supplier)': getSponsoringSupplierId(company),
    'Referral ID': '', // placeholder
    'Setup Fee': ReportFormatter.addCurrencyCell(0), // currently is not handled by BT
    'Subscription Fee': ReportFormatter.addCurrencyCell(0), // currently is not handled by BT
    'Credit Limit': ReportFormatter.addCurrencyCell(company.credit?.limit),

    'ATC enabled': '', // TODO: VK: same customer can have different settings for different suppliers
    'ATC threshold amount': '', // TODO: VK: same customer can have different settings for different suppliers
    'Authorization enabled': 'Yes', // not supported in Authorizations phase 1, enabled by default
    'Realtime Draw Approval': '', // placeholder

    // Account Activity
    'Amount on Hold': ReportFormatter.addCurrencyCell(holdAmount),
    'Credit Available': ReportFormatter.addCurrencyCell(CompanyUtils.getAvailableCreditAmount(company.credit, holdAmount)),
    'Account Balance': ReportFormatter.addCurrencyCell(company.credit?.balance),
    'Total draws since issuance': company.applications.length + 1,
    'Total draws amount since issuance': ReportFormatter.addCurrencyCell(getTotalDrawsAmount(company)),
    '# of installments repaid since LOC issuance': paymentsSummary.countPaid,
    '$ amount repaid on all installments': ReportFormatter.addCurrencyCell(paymentsSummary.totalPaid),
    '% of installments paid on time': paymentsSummary.onTimePaymentsPercentage,
    '# late payments': paymentsSummary.countLate,
    '$ late payments': ReportFormatter.addCurrencyCell(paymentsSummary.totalLate),

    'Revenue generated for BT to date': ReportFormatter.addCurrencyCell(revenue.totalRevenue),
    'Total activity fees and interests': ReportFormatter.addCurrencyCell(revenue.totalLoanFees),
    'Late Fee Cumulative Amount': ReportFormatter.addCurrencyCell(revenue.totalLateFees),
    'Reschedule Fee Cumulative Amount': ReportFormatter.addCurrencyCell(revenue.totalExtensionFees),
    'Penalty Interest Cumulative Amount': ReportFormatter.addCurrencyCell(revenue.totalPenaltyInterest),
    'Late Fee Outstanding Amount': ReportFormatter.addCurrencyCell(outstandingFees.lateFees),
    'Reschedule Fee Outstanding Amount': ReportFormatter.addCurrencyCell(outstandingFees.extensionFees),
    'Penalty Interest Outstanding Amount': ReportFormatter.addCurrencyCell(outstandingFees.penaltyInterest),
    'Total supplier fees': ReportFormatter.addCurrencyCell(revenue.totalSupplierFees),
    'Total setup and subscription fees': ReportFormatter.addCurrencyCell(0), // not handled by BT
    'DACA account used': '', // placeholder
    'currently late?': paymentsSummary.currentlyLate ? 1 : 0,

    'u/w data modified from system providers?': '', // placeholder
    'exceptions made to u/w?': '', // placeholder
    'modified payment plan?': '', // placeholder
    'in collections?': '', // placeholder
    'in default?': '', // placeholder
    'charged off': '', // placeholder
    'ucc in place?': '', // placeholder
    'mechanics lien notice sent?': '', // placeholder
    'mechanics lien in place?': '', // placeholder
    'in litigation?': '', // placeholder
    'recovered': '', // placeholder

    // Account Initial Underwriting data
    'Company bankruptcy in the last 24 months': yesForRejected(firstKnockout.companyBankruptcy),
    'Any $5,000+ judgement in the past 12 months': yesForRejected(firstKnockout.judgments),
    'Any $5,000+ lien in the past 12 months': yesForRejected(firstKnockout.liens),
    'Lexis Nexis BVI score': formatNumericScore(firstKnockout.BVI),
    'Lexis Nexis BRI score': formatNumericScore(firstKnockout.BRI),
    'Years In Business': getBusinessAge(firstDraft),
    'Phone number on file with bank matches number on file with BT': '', // TODO: VK: Plaid

    // principal owner initial
    'Principal Owner First Name': firstDraft?.businessOwner_firstName,
    'Principal Owner Last Name': firstDraft?.businessOwner_lastName,
    "Latest principal owner's FICo. acceptable is >620) or null":
      firstKnockout.FICO?.score ?? '',
    "Principal owner's personal bankruptcy in the last 24 months": yesForRejected(firstKnockout.personalBankruptcy),
    'Principal Owner Lexis Nexis CVI score': formatNumericScore(firstKnockout.CVI),
    'Principal Owner Lexis Nexis CRI score': formatNumericScore(firstKnockout.CRI),
    'Principal Owner Email age': formatNumericScore(firstKnockout.emailAge),

    // co-owners initial
    'Co-owner 1 First Name / Business Entity': firstCoOwnersReport[0]?.firstOrBusinessName ?? '',
    'Co-owner 1 Last Name': firstCoOwnersReport[0]?.lastName ?? '',
    "Latest co-owner1's FICo. acceptable is >620) or null": firstCoOwnersReport[0]?.FICO ?? '',
    "Co-owner1's personal bankruptcy in the last 24 months": firstCoOwnersReport[0]?.bankruptcy ?? '',
    'Co-owner 1 Lexis Nexis CVI score': firstCoOwnersReport[0]?.CVI ?? '',
    'Co-owner 1 Lexis Nexis CRI score': firstCoOwnersReport[0]?.CRI ?? '',
    'Co-owner 1 Email age': firstCoOwnersReport[0]?.emailAge ?? '',

    'Co-owner 2 First Name / Business Entity': firstCoOwnersReport[1]?.firstOrBusinessName ?? '',
    'Co-owner 2 Last Name': firstCoOwnersReport[1]?.lastName ?? '',
    "Latest co-owner2's FICo. acceptable is >620) or null": firstCoOwnersReport[1]?.FICO ?? '',
    "Co-owner2's personal bankruptcy in the last 24 months": firstCoOwnersReport[1]?.bankruptcy ?? '',
    'Co-owner 2 Lexis Nexis CVI score': firstCoOwnersReport[1]?.CVI ?? '',
    'Co-owner 2 Lexis Nexis CRI score': firstCoOwnersReport[1]?.CRI ?? '',
    'Co-owner 2 Email age': firstCoOwnersReport[1]?.emailAge ?? '',

    'Co-owner 3 First Name / Business Entity': firstCoOwnersReport[2]?.firstOrBusinessName ?? '',
    'Co-owner 3 Last Name': firstCoOwnersReport[2]?.lastName ?? '',
    "Latest co-owner3's FICo. acceptable is >620) or null": firstCoOwnersReport[2]?.FICO ?? '',
    "Co-owner3's personal bankruptcy in the last 24 months": firstCoOwnersReport[2]?.bankruptcy ?? '',
    'Co-owner 3 Lexis Nexis CVI score': firstCoOwnersReport[2]?.CVI ?? '',
    'Co-owner 3 Lexis Nexis CRI score': firstCoOwnersReport[2]?.CRI ?? '',
    'Co-owner 3 Email age': firstCoOwnersReport[2]?.emailAge ?? '',

    'Business Experian Credit Score': firstCreditStatus?.reliabilityCode ?? '',
    '% of Account +60 DBT': firstCreditStatus?.tradelinesPercentage ?? '',
    'Amount of Account +60 DBT': ReportFormatter.addCurrencyCell(firstCreditStatus?.tradelinesDebt),
    'Business Experian Total Trade Lines': ReportFormatter.addCurrencyCell(firstCreditStatus.tradelinesBalance),
    'Experian debt-to-credit ratio with non-BT trade lines': '', // placeholder
    'Experian Monthly Debt Obligations': '', // placeholder
    'Self Reported Business Debt': ReportFormatter.addCurrencyCell(firstDraft?.finance_debt),
    'Self Reported Business Income': ReportFormatter.addCurrencyCell(firstDraft?.finance_revenue),
    'Six month average bank balance': ReportFormatter.addCurrencyCell(getSixMonthAverageBalance(firstApp)),
    'BT-computed Yearly Income based on Plaid Data': '', // TODO: VK: Plaid
    'Yearly Business Income': ReportFormatter.addCurrencyCell(firstLoanDecision?.decision?.annual_revenue),
    'Average Monthly Cashflow': ReportFormatter.addCurrencyCell(getAverageMonthlyCashFlow(firstApp)),
    'Estimated revenue is within 20% of self-reported revenue': isEstimatedRevenueWithinSelfReported(firstLoanDecision),
    'DTI1 (actual DTI based on monthly obligations before issuance)': '', // TODO: VK: Plaid
    'DTI2 (based on total debt before issuance)': '', // TODO: VK: Plaid
    'Credit Policy used': firstApp.metadata?.creditPolicyVersion ?? 'v2.0',

    // Account latest underwriting data
    'Company bankruptcy in the last 24 months (latest)': yesForRejected(lastKnockout.companyBankruptcy),
    'Any $5,000+ judgement in the past 12 months (latest)': yesForRejected(lastKnockout.judgments),
    'Any $5,000+ lien in the past 12 months (latest)': yesForRejected(lastKnockout.liens),
    'Lexis Nexis BVI score (latest)': formatNumericScore(lastKnockout.BVI),
    'Lexis Nexis BRI score (latest)': formatNumericScore(lastKnockout.BRI),

    // principal owner latest
    "Latest principal owner's FICo. acceptable is >620) or null (latest)": firstKnockout.FICO?.score ?? '',
    "Principal owner's personal bankruptcy in the last 24 months (latest)": yesForRejected(firstKnockout.personalBankruptcy),
    'Principal Owner Lexis Nexis CVI score (latest)': formatNumericScore(firstKnockout.CVI),
    'Principal Owner Lexis Nexis CRI score (latest)': formatNumericScore(firstKnockout.CRI),
    'Principal Owner Email age (latest)': formatNumericScore(firstKnockout.emailAge),

    // co-owners latest
    'Co-owner 1 First Name / Business Entity (latest)': lastCoOwnersReport[0]?.firstOrBusinessName ?? '',
    'Co-owner 1 Last Name (latest)': lastCoOwnersReport[0]?.lastName ?? '',
    "Latest co-owner1's FICo. acceptable is >620) or null (latest)": lastCoOwnersReport[0]?.FICO ?? '',
    "Co-owner1's personal bankruptcy in the last 24 months (latest)": lastCoOwnersReport[0]?.bankruptcy ?? '',
    'Co-owner 1 Lexis Nexis CVI score (latest)': lastCoOwnersReport[0]?.CVI ?? '',
    'Co-owner 1 Lexis Nexis CRI score (latest)': lastCoOwnersReport[0]?.CRI ?? '',
    'Co-owner 1 Email age (latest)': lastCoOwnersReport[0]?.emailAge ?? '',

    'Co-owner 2 First Name / Business Entity (latest)': lastCoOwnersReport[1]?.firstOrBusinessName ?? '',
    'Co-owner 2 Last Name (latest)': lastCoOwnersReport[1]?.lastName ?? '',
    "Latest co-owner2's FICo. acceptable is >620) or null (latest)": lastCoOwnersReport[1]?.FICO ?? '',
    "Co-owner2's personal bankruptcy in the last 24 months (latest)": lastCoOwnersReport[1]?.bankruptcy ?? '',
    'Co-owner 2 Lexis Nexis CVI score (latest)': lastCoOwnersReport[1]?.CVI ?? '',
    'Co-owner 2 Lexis Nexis CRI score (latest)': lastCoOwnersReport[1]?.CRI ?? '',
    'Co-owner 2 Email age (latest)': lastCoOwnersReport[1]?.emailAge ?? '',

    'Co-owner 3 First Name / Business Entity (latest)': lastCoOwnersReport[2]?.firstOrBusinessName ?? '',
    'Co-owner 3 Last Name (latest)': lastCoOwnersReport[2]?.lastName ?? '',
    "Latest co-owner3's FICo. acceptable is >620) or null (latest)": lastCoOwnersReport[2]?.FICO ?? '',
    "Co-owner3's personal bankruptcy in the last 24 months (latest)": lastCoOwnersReport[2]?.bankruptcy ?? '',
    'Co-owner 3 Lexis Nexis CVI score (latest)': lastCoOwnersReport[2]?.CVI ?? '',
    'Co-owner 3 Lexis Nexis CRI score (latest)': lastCoOwnersReport[2]?.CRI ?? '',
    'Co-owner 3 Email age (latest)': lastCoOwnersReport[2]?.emailAge ?? '',

    'Business Experian Credit Score (latest)': lastCreditStatus?.reliabilityCode ?? '',
    '% of Account +60 DBT (latest)': lastCreditStatus?.tradelinesPercentage ?? '',
    'Amount of Account +60 DBT (latest)': ReportFormatter.addCurrencyCell(lastCreditStatus?.tradelinesDebt),
    'Business Experian Total Trade Lines (latest)': ReportFormatter.addCurrencyCell(lastCreditStatus.tradelinesBalance),
    'Experian debt-to-credit ratio with non-BT trade lines (latest)': '', // placeholder
    'Experian Monthly Debt Obligations (latest)': '', // placeholder
    'Six month average bank balance (latest)': ReportFormatter.addCurrencyCell(getSixMonthAverageBalance(lastApp)),
    'BT-computed Yearly Income based on Plaid Data (latest)': '', // TODO: VK: Plaid
    'Yearly Business Income (latest)': ReportFormatter.addCurrencyCell(lastLoanDecision?.decision?.annual_revenue),
    'Average Monthly Cashflow (latest)': ReportFormatter.addCurrencyCell(getAverageMonthlyCashFlow(lastApp)),
    'Estimated revenue is within 20% of self-reported revenue (latest)': isEstimatedRevenueWithinSelfReported(lastLoanDecision),
    'DTI1 (actual DTI based on monthly obligations before issuance) (latest)': '', // TODO: VK: Plaid
    'DTI2 (based on total debt before issuance) (latest)': '', // TODO: VK: Plaid
    'Credit Policy used (latest)': lastApp.metadata?.creditPolicyVersion ?? 'v2.0',

    // Account Summary
    'Business Name': formatBusinessName(firstDraft),
    'Address': firstDraft?.businessInfo_businessAddress?.address ?? '',
    'City': firstDraft?.businessInfo_businessAddress?.city ?? '',
    'State': abbreviateState(firstDraft),
    'Zip': firstDraft?.businessInfo_businessAddress?.zip ?? '',
  }
}

function getSponsoringSupplierId(company: ICompanyLoans) {
  if (company.type === 'supplier' && company?.settings?.invitedBy) {
    if (
      company.applications.some(
        (app) => app.supplier?._id?.toString() === company.settings.invitedBy,
      )
    ) {
      return company.settings.invitedBy
    }
  }
  return ''
}

function getBusinessAge(draft: IUnifiedApplicationDraft | null) {
  let startDate = draft?.businessInfo_startDate

  if (!startDate) return 'n/a'

  // there are legacy records without month in start date
  if (/^\d{4}$/.test(startDate)) {
    // Prepend January as the default month for simplicity
    startDate = `01/${startDate}`
  }

  return moment().diff(moment.tz(startDate, 'MM/YYYY', 'UTC'), 'years')
}

function getTotalDrawsAmount(company: ICompanyLoans) {
  return company.applications
    .map((app) => app.lmsLoan?.amount ?? 0)
    .reduce((total, loanAmount) => total + loanAmount, 0)
}

function getPaymentsSummary(company: ICompanyLoans) {
  const receivables = company.applications
    .filter((app) => !!app.lmsLoan?.loanReceivables)
    .flatMap((app) =>
      app.lmsLoan!.loanReceivables.filter(
        (r) =>
          r.scheduleStatus === ScheduleStatus.Current &&
          r.status !== ReceivableStatus.Canceled,
      ),
    )

  const paidReceivables = receivables.filter(
    (r) => r.status === ReceivableStatus.Paid,
  )

  const paidLateReceivables = receivables.filter(
    (r) =>
      r.status === ReceivableStatus.Paid &&
      moment(r.paidDate).isAfter(moment(r.expectedDate)),
  )

  const unpaidLateReceivables = receivables.filter(
    (r) =>
      // overdue handled by overdue detector
      r.status === ReceivableStatus.Late ||
      // overdue not yet handled by overdue detector
      (r.status === ReceivableStatus.Pending &&
        moment().isAfter(moment.tz(r.expectedDate, 'UTC'))),
  )

  const countTotal = receivables.length
  const countPaid = paidReceivables.length
  const countPaidLate = paidLateReceivables.length
  const countUnpaidLate = unpaidLateReceivables.length

  const countLate = countUnpaidLate + countPaidLate
  const countPaidOnTime = countPaid - countPaidLate

  return {
    countPaid,
    totalPaid: sum(paidReceivables.map((r) => r.paidAmount)),
    onTimePaymentsPercentage: countTotal
      ? round((countPaidOnTime / countTotal) * 100, 2)
      : 0,
    countLate,
    totalLate:
      sum(paidLateReceivables.map((r) => r.paidAmount)) +
      sum(unpaidLateReceivables.map((r) => r.expectedAmount + r.adjustAmount)),
    currentlyLate: !!countUnpaidLate,
  }
}

async function getRevenue(
  company: ICompanyLoans,
  paymentPlans: Map<string, ILoanPaymentPlan | null>,
  pricingPackages: Map<string, ILoanPricingPackage | null>,
) {
  const loanFees = company.applications
    .filter((app) => !!app.lmsLoan?.loanReceivables)
    .flatMap((app) =>
      app.lmsLoan!.loanReceivables.filter(
        (r) =>
          r.status === ReceivableStatus.Paid &&
          [
            ReceivableType.ExtensionFee,
            ReceivableType.LoanFee,
            ReceivableType.LatePaymentFee,
            ReceivableType.ManualLatePaymentFee,
            ReceivableType.PenaltyInterestFee,
          ].includes(r.type),
      ),
    )

  const otherRevenue = await Promise.all(
    company.applications.map(async (app) => {
      let supplierFee = 0
      let interchangeOnVC = 0

      const paymentPlan = await getPaymentPlan(app, paymentPlans)
      const type = getLoanType(app, paymentPlan)

      if (type === 'VC') {
        interchangeOnVC = getInterchangeOnVC(app.lmsLoan).amount
      } else {
        const loanPackage = await getPricingPackage(
          app,
          pricingPackages,
          app.supplier,
        )

        const supplierFeeRate = loanPackage?.merchant || 0
        const discountedAmount =
          ((100 - supplierFeeRate) * (app.approvedAmount || 0)) / 100
        supplierFee = round((app.approvedAmount || 0) - discountedAmount, 2)
      }

      return {
        interchangeOnVC,
        supplierFee,
      }
    }),
  )

  const totalLateFees = getReceivablesCollectedTotal(loanFees, [
    ReceivableType.LatePaymentFee,
    ReceivableType.ManualLatePaymentFee,
  ])

  const totalPenaltyInterest = getReceivablesCollectedTotal(
    loanFees,
    ReceivableType.PenaltyInterestFee,
  )

  const totalExtensionFees = getReceivablesCollectedTotal(
    loanFees,
    ReceivableType.ExtensionFee,
  )

  const totalLoanFees = sum(loanFees.map((f) => f.paidAmount))
  const totalSupplierFees = sum(otherRevenue.map((r) => r.supplierFee))
  const totalInterchangeOnVC = sum(otherRevenue.map((r) => r.interchangeOnVC))
  const totalRevenue = totalLoanFees + totalInterchangeOnVC + totalSupplierFees

  return {
    totalRevenue,
    totalLoanFees,
    totalLateFees,
    totalExtensionFees,
    totalPenaltyInterest,
    totalSupplierFees,
  }
}

function getOutstandingFees(company: ICompanyLoans) {
  const unpaid = company.applications
    .filter((app) => !!app.lmsLoan?.loanReceivables)
    .flatMap((app) =>
      app.lmsLoan!.loanReceivables.filter(
        (r) =>
          [ReceivableStatus.Pending, ReceivableStatus.Late].includes(
            r.status,
          ) && r.scheduleStatus === ScheduleStatus.Current,
      ),
    )

  const lateFees = getReceivablesOutstandingTotal(unpaid, [
    ReceivableType.LatePaymentFee,
    ReceivableType.ManualLatePaymentFee,
  ])

  const extensionFees = getReceivablesOutstandingTotal(
    unpaid,
    ReceivableType.ExtensionFee,
  )

  const penaltyInterest = getReceivablesOutstandingTotal(
    unpaid,
    ReceivableType.PenaltyInterestFee,
  )

  return {
    lateFees,
    extensionFees,
    penaltyInterest,
  }
}

async function getLatestAgreements(companies: ICompany[]) {
  const agreements = new Map<string, string>()

  // not using parallel here - postgres may fail with timeout on multiple parallel requests
  for (const company of companies) {
    const latestDocument = await DocumentVersioningService.getLatestDocument(
      company._id.toString(),
      null,
      AgreementType.MASTER_AGREEMENT,
    )

    agreements.set(
      company._id.toString(),
      latestDocument?.template?.semanticVersion ?? '',
    )
  }

  return agreements
}

function isEstimatedRevenueWithinSelfReported({ decision }: OutputData) {
  return (decision?.annual_revenue ?? 0) <=
    (decision?.debt_estimate_provided_in_app ?? 0) * 0.2
    ? 'Yes'
    : 'No'
}

function formatBusinessName(draft: IUnifiedApplicationDraft | null) {
  return (
    draft?.businessInfo_businessName?.dba ||
    draft?.businessInfo_businessName?.legalName ||
    ''
  )
}
