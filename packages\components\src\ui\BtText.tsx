import { Text, TextProps } from '@ui-kitten/components'
import React, { forwardRef } from 'react'
import { composeStyle } from '../helpers'
import { StyleSheet } from 'react-native'

export interface BtTextProps extends TextProps {
  size?: number
  color?: string
  weight?: string
}

export const BtText = forwardRef<Text, BtTextProps>(
  (
    {
      size = 14,
      color = '#335C75',
      weight = '400',
      style,
      ...rest
    }: BtTextProps,
    ref: React.Ref<Text>,
  ) => (
    <Text
      ellipsizeMode="tail"
      style={composeStyle(
        { fontSize: size, fontWeight: weight },
        color && { color },
        style,
      )}
      ref={ref}
      {...rest}
    />
  ),
)

export const BtTitle = forwardRef<Text, BtTextProps>(
  ({ ...rest }: BtTextProps, ref: React.Ref<Text>) => (
    <BtPlainText size={20} weight={'600'} ref={ref} {...rest} />
  ),
)

export const BtSubTitle = forwardRef<Text, BtTextProps>(
  ({ ...rest }: BtTextProps, ref: React.Ref<Text>) => (
    <BtPlainText ref={ref} {...rest} />
  ),
)

export const BtPlainText = forwardRef<Text, BtTextProps>(
  ({ style, ...rest }: BtTextProps, ref: React.Ref<Text>) => (
    <BtText style={[styles.plainText, style]} ref={ref} {...rest} />
  ),
)

export const BtTableText = forwardRef<Text, BtTextProps>(
  ({ ...rest }: BtTextProps, ref: React.Ref<Text>) => (
    <BtText color={'#2F4858'} size={13} weight={'500'} ref={ref} {...rest} />
  ),
)

const styles = StyleSheet.create({
  plainText: {
    fontFamily: 'Inter, sans-serif',
    color: '#001929',
  },
})
