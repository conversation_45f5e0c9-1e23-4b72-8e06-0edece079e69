import React from 'react'
import ApplicationStore, {
  ApplicationSubmissionStage,
} from '../../GeneralApplication/Application/ApplicationStore'
import Application from '../../GeneralApplication/Application'
import { observer } from 'mobx-react-lite'
import { ScreenWithCobrandingHeader } from '../../../ui/white-label-components/ScreenWithCobrandingHeader'
import { paths } from '../../links'
import { useNavigation } from '@react-navigation/core'
import { useInvoiceDetailsContext } from '../TabInvoice/InvoiceDetailsContext'
import { ApplicationWithPaymentFlow } from '../InvoicePayment/ApplicationWithPayment/ApplicationWithPaymentFlow'

export const CreditRequest = observer(() => {
  const { setPayAndSubmitApplication, invoiceDetails, submissionStage } =
    ApplicationStore

  const navigation = useNavigation()
  const { setPaymentPlanModalVisibility } = useInvoiceDetailsContext()

  setPayAndSubmitApplication(true)

  const handlePaymentClose = () => {
    setPaymentPlanModalVisibility(false)

    ApplicationStore.adjustFinalStep()
    ApplicationStore.setSubmissionStage(ApplicationSubmissionStage.APPLICATION)
  }

  return (
    <ScreenWithCobrandingHeader>
      <>
        {submissionStage === ApplicationSubmissionStage.APPLICATION && (
          <Application />
        )}

        {submissionStage === ApplicationSubmissionStage.PAYMENT && (
          <ApplicationWithPaymentFlow
            invoices={invoiceDetails}
            onClose={handlePaymentClose}
            onSuccess={() => {
              handlePaymentClose()
              navigation.navigate(paths.Console._self)
            }}
          />
        )}
      </>
    </ScreenWithCobrandingHeader>
  )
})
