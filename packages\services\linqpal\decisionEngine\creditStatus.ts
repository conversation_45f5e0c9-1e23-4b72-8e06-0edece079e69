import moment from 'moment'
import { Company, Experian } from '@linqpal/common-backend'
import { decrypt, Wrapper } from './wrapper'
import { ILoanApplication } from '@linqpal/common-backend/src/models/types'
import { States, statesHashMap } from '@linqpal/models/src/dictionaries'
import { ILinqpalEvent } from '../types'
import {
  IOwnerInfo,
  IUnifiedApplicationDraft,
  IExperianOwnersData,
  IExperianBusinessData,
} from '@linqpal/models'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import md5 from 'crypto-js/md5'

interface IIndividualOwnerRequest {
  body: {
    ownerName: {
      firstName: string
      lastName: string
    }
    currentAddress: {
      street: string
      city: string
      state: string
      zip: string
    }
    ssn: string
    dob: {
      day: number
      month: number
      year: number
    }
  }
  ownerId?: string
  isPrincipal?: boolean
  key: string
}

interface IEntityOwnerRequest {
  body: {
    name: string
    state: string
    city: string
    street: string
    zip: string
    taxId: string
  }
  ownerId?: string
  isPrincipal?: boolean
  key: string
}

const bankruptcyStatusCodes = [
  '13',
  '15',
  '16',
  '17',
  '22',
  '23',
  '24',
  '25',
  '26',
  '27',
  '28',
  '29',
]

export async function creditStatus(event: ILinqpalEvent) {
  return Wrapper(
    'creditStatus',
    event,
    async (application: ILoanApplication) => {
      const { company_id, draft } = application
      const company = await Company.findOne({ _id: company_id })
      if (!company || !draft) return

      Experian.init()
      await Experian.login()

      const individualOwners = draft.owners.filter(
        (owner) => owner.type === OwnerTypes.INDIVIDUAL,
      )

      const individualRequests = await Promise.all([
        createPrincipalOwnerRequest(draft),
        ...individualOwners.map((owner) => createCoOwnerRequest(owner)),
      ])

      const ownersData = await Promise.all(
        individualRequests.map(async (request) => {
          const owner = {
            id: request.ownerId,
            firstName: request.body.ownerName.firstName,
            lastName: request.body.ownerName.lastName,
            isPrincipal: request.isPrincipal,
            type: OwnerTypes.INDIVIDUAL,
            key: md5(request.key).toString(),
          }

          try {
            const response = await Experian.reportsBop(
              [request.body],
              request.key,
            )
            return {
              owner,
              ...mapIndividualScores(response[0] ?? {}),
            }
          } catch (e: any) {
            return { owner, error: e?.message }
          }
        }),
      )

      // Get business information
      const businesses = draft.owners.filter(
        (owner) => owner.type === OwnerTypes.ENTITY,
      )

      const principalEin = await decrypt(draft.businessInfo_ein)

      const businessRequests = await Promise.all([
        createPrincipalBusinessRequest(draft, principalEin),
        ...businesses.map(async (owner) =>
          createOwnerEntityRequest(owner, principalEin),
        ),
      ])

      const businessData = await Promise.all(
        businessRequests.map((request) =>
          Experian.search(request.body, request.key)
            .then((response) =>
              response.length
                ? mapExperianReport(response[0], request.key)
                : null,
            )
            .then((experianReport) => ({
              owner: {
                id: request.ownerId,
                businessName: request.body.name,
                type: OwnerTypes.ENTITY,
                isPrincipal: request.isPrincipal,
                key: md5(request.key).toString(),
              },
              ...experianReport,
            })),
        ),
      )

      return {
        businessData,
        ownersData,
      }
    },
  )
}

async function createPrincipalOwnerRequest(
  draft: IUnifiedApplicationDraft,
): Promise<IIndividualOwnerRequest> {
  const ssn = await decrypt(draft.businessOwner_ssn)
  const address = draft.businessOwner_address
  const momentBirthday = moment(draft.businessOwner_birthdate, 'MM/DD/YYYY')

  return {
    body: {
      ownerName: {
        firstName: draft.businessOwner_firstName,
        lastName: draft.businessOwner_lastName,
      },
      currentAddress: {
        street: address?.address || '',
        city: address?.city || '',
        state: statesHashMap[address?.state as States],
        zip: address?.zip || '',
      },
      ssn: ssn,
      dob: {
        day: momentBirthday.date(),
        month: momentBirthday.month() + 1,
        year: momentBirthday.year(),
      },
    },
    ownerId: draft.businessOwner_id,
    isPrincipal: true,
    key: ssn,
  }
}

async function createCoOwnerRequest(
  owner: IOwnerInfo,
): Promise<IIndividualOwnerRequest> {
  const ssn = await decrypt(owner.ssn)
  const momentBirthday = moment(owner.birthday, 'MM/DD/YYYY')

  return {
    body: {
      ownerName: {
        firstName: owner.firstName,
        lastName: owner.lastName,
      },
      currentAddress: {
        street: owner.address,
        city: owner.city,
        state: statesHashMap[owner.state as States],
        zip: owner.zip,
      },
      ssn: ssn,
      dob: {
        day: momentBirthday.date(),
        month: momentBirthday.month() + 1,
        year: momentBirthday.year(),
      },
    },
    ownerId: owner.id,
    key: ssn,
  }
}

async function createPrincipalBusinessRequest(
  draft: IUnifiedApplicationDraft,
  ein: string,
): Promise<IEntityOwnerRequest> {
  const address = draft.businessInfo_businessAddress
  return {
    body: {
      name:
        draft.businessInfo_businessName.legalName ||
        draft.businessInfo_businessName.dba,
      state: statesHashMap[address?.state as States],
      city: address?.city,
      street: address?.address,
      zip: address?.zip,
      taxId: ein,
    },
    ownerId: undefined, // applicable to business owners only, not to business itself
    isPrincipal: true,
    key: ein,
  }
}

async function createOwnerEntityRequest(
  owner: IOwnerInfo,
  principalEin: string,
): Promise<IEntityOwnerRequest> {
  const ein = await decrypt(owner.ein)

  return {
    body: {
      name: owner.entityName,
      state: statesHashMap[owner.state as States],
      city: owner.city,
      street: owner.address,
      zip: owner.zip,
      taxId: ein,
    },
    ownerId: owner.id,
    // should be aligned with co-owner Entity key in other steps (KYB)
    key: ein?.concat(principalEin),
  }
}

function mapIndividualScores(
  experianReport: any,
): Omit<IExperianOwnersData, 'owner'> {
  const locked = experianReport.statement?.some(
    (s: any) =>
      s.statementText?.startsWith('32&') || s?.statementText.startsWith('25&'),
  )

  return {
    pastDueAmount: experianReport.profileSummary?.pastDueAmount,
    inquiriesDuringLast6Months:
      experianReport.profileSummary?.inquiriesDuringLast6Months,
    score: locked ? 'LOCKED' : experianReport.riskModel?.[0]?.score,
    lastBankruptcyDate: experianReport.publicRecord
      ?.filter((record: any) =>
        bankruptcyStatusCodes.includes(record.status?.code),
      )
      .sort((r1: any, r2: any) => (r1.filingDate >= r2.filingDate ? -1 : 1))[0]
      .filingDate,
  }
}

async function mapExperianReport(
  experianCompany: any,
  key: string,
): Promise<Omit<IExperianBusinessData, 'owner'>> {
  const { bin, reliabilityCode } = experianCompany

  const [bankruptcies, judgments, liens, trades, experianCreditStatus] =
    await Promise.all([
      Experian.bankruptcies(bin, key),
      Experian.judgments(bin, key),
      Experian.liens(bin, key),
      Experian.trades(bin, key),
      Experian.creditStatus(bin, key),
    ])

  const { bankruptcyIndicator } = bankruptcies.bankruptcySummary
  const { judgmentBalance } = judgments.judgmentSummary
  const { lienBalance } = liens.lienSummary
  const { yearsOnFile, combinedAccountBalance } = experianCreditStatus
  const { tradePaymentTotals } = trades
  let DBTTradeLines = 0
  const {
    dbt60 = 0,
    dbt90 = 0,
    dbt91Plus = 0,
    totalAccountBalance = { amount: 0 },
  } = tradePaymentTotals?.tradelines || {}
  if (totalAccountBalance?.amount) {
    DBTTradeLines =
      ((dbt60 + dbt90 + dbt91Plus) / 100) * totalAccountBalance.amount
  }

  return {
    reliabilityCode,
    bankruptcyIndicator,
    judgmentBalance,
    lienBalance,
    accountBalanceDebt: combinedAccountBalance,
    tradelinesBalance: totalAccountBalance?.amount,
    tradelinesPercentage: dbt60 + dbt90 + dbt91Plus,
    tradelinesDebt: DBTTradeLines,
    yearsOnFile,
  }
}
