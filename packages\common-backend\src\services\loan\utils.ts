import {
  ICompany,
  ILoanApplication,
  ILoanPaymentPlan,
  ILoanPricingPackage,
} from '../../models/types'
import { getPlan } from '../loanplan.service'
import * as LMS from '../lms.service'
import { ICredit, ILoan, IReceivable, ReceivableType } from '../lms.service'
import { Company, Invoice } from '../../models'
import { getLoanPricingPackage } from '../package.service'
import {
  ILoanPaymentPlanModel,
  IScore,
  IUnifiedApplicationDraft,
  ScoringResult,
} from '@linqpal/models'
import round from 'lodash/round'
import { IKnockoutInfo } from './knockoutReader'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import moment from 'moment-timezone'
import sum from 'lodash/sum'
import { States, statesHashMap } from '@linqpal/models/src/dictionaries'
import { onBoardingService } from '../onBoarding/onBoarding.service'
import { IDrawApproval } from '../onBoarding/types'

export async function getPaymentPlan(
  app: ILoanApplication,
  paymentPlans?: Map<string, ILoanPaymentPlan | null>,
) {
  if (app.metadata?.paymentPlan) {
    return app.metadata?.paymentPlan
  } else if (app.invoiceDetails?.paymentPlan) {
    try {
      const planName = app.invoiceDetails?.paymentPlan
      let plan = paymentPlans?.get(planName)

      if (!plan) {
        plan = await getPlan(planName)
        paymentPlans?.set(planName, plan)
      }

      return plan
    } catch (e) {
      console.log(e)
    }
  }

  return null
}

export async function getPricingPackage(
  app: ILoanApplication,
  pricingPackages?: Map<string, ILoanPricingPackage | null>,
  supplier?: ICompany | null,
) {
  let payeeCompany = supplier
  if (!payeeCompany) {
    const invoiceId =
      app.invoiceDetails?.invoiceId?.[0] ?? app.invoiceDetails?.invoiceId
    const invoice = invoiceId
      ? await Invoice.findById(app.invoiceDetails.invoiceId)
      : null

    payeeCompany = invoice?.company_id
      ? await Company.findById(invoice.company_id)
      : null
  }

  let loanPackage = app.metadata?.loanPackage
  if (!loanPackage) {
    const packageName = payeeCompany?.settings.loanPricingPackageId
    if (packageName) {
      let pricingPackage = pricingPackages?.get(packageName) ?? null

      if (!pricingPackage) {
        pricingPackage = await getLoanPricingPackage(packageName)
        pricingPackages?.set(packageName, pricingPackage)
      }

      if (pricingPackage) {
        loanPackage = pricingPackage.metadata
      }
    }
  }

  return loanPackage
}

export function getCohort(
  app: ILoanApplication,
  firstEverApp: ILoanApplication,
) {
  const firstCohort = moment(firstEverApp.issueDate).startOf('month')
  return (
    firstCohort.month() + 1 + moment(app.issueDate).diff(firstCohort, 'month')
  )
}

export function getLoanType(
  app: ILoanApplication,
  paymentPlan: ILoanPaymentPlanModel | null,
) {
  return app.invoiceDetails?.cardId
    ? 'VC'
    : paymentPlan?.frequency === 'single'
    ? 'BT Single'
    : paymentPlan?.type === 'nosupplier'
    ? 'BT NS'
    : 'BT Regular'
}

export function isLoanNotFinanciallyActive(lmsInfo: ILoan | null): boolean {
  return lmsInfo?.status === 'Refinanced' || lmsInfo?.status === 'Canceled'
}

export function getInterchangeOnVC(loan: ILoan | null) {
  const interchangeRate = 1.76
  const invoiceAmount = round(loan?.amount ?? 0, 2)

  return {
    rate: interchangeRate,
    amount: round((invoiceAmount * (interchangeRate / 2)) / 100, 2),
  }
}

export function getCashFlow(app: ILoanApplication) {
  return (
    app.outputs?.find((output) =>
      ['ProcessManualData', 'ProcessFinicityData', 'ProcessPlaidData'].includes(
        output.step,
      ),
    )?.data?.cashFlow || []
  )
}

export function getSixMonthAverageBalance(app: ILoanApplication) {
  const cashFlow = getCashFlow(app)

  const sixMonthAgo = app.issueDate
    ? moment(app.issueDate).subtract(6, 'month').startOf('month')
    : null

  const lastSixMonthCashFlow = sixMonthAgo
    ? cashFlow.filter(
        (flow) => !flow.date || sixMonthAgo.isSameOrBefore(flow.date),
      )
    : []

  return lastSixMonthCashFlow.length > 0
    ? round(
        sum(lastSixMonthCashFlow.map((flow) => flow.balance)) /
          lastSixMonthCashFlow.length,
        2,
      )
    : 0
}

export function getAverageMonthlyCashFlow(app: ILoanApplication) {
  const cashFlow = getCashFlow(app)

  return cashFlow.length > 0
    ? round(sum(cashFlow.map((flow) => flow.cashFlow)) / cashFlow.length, 2)
    : 0
}

export function mapCoOwnersKnockouts(knockout: IKnockoutInfo) {
  return knockout.coOwners.map((owner) => ({
    firstOrBusinessName:
      owner.type === OwnerTypes.INDIVIDUAL
        ? owner.firstName
        : owner.businessName,
    lastName: owner.type === OwnerTypes.INDIVIDUAL ? owner.lastName : '',
    bankruptcy: yesForRejected(
      owner.type === OwnerTypes.INDIVIDUAL
        ? owner.personalBankruptcy
        : owner.companyBankruptcy,
    ),
    FICO: owner.FICO?.score ?? '',
    CVI: formatNumericScore(
      owner.type === OwnerTypes.INDIVIDUAL ? owner.CVI : owner.BVI,
    ),
    CRI: formatNumericScore(
      owner.type === OwnerTypes.INDIVIDUAL ? owner.CRI : owner.BRI,
    ),
    emailAge:
      owner.type === OwnerTypes.INDIVIDUAL
        ? formatNumericScore(owner.emailAge)
        : '',
  }))
}

export async function getLoans(apps: ILoanApplication[]) {
  const responses: ILoan[][] = []
  const batchSize = 40
  const batchesCount = Math.ceil(apps.length / batchSize)

  for (let i = 0; i < batchesCount; i += 1) {
    const loanIds = apps
      .slice(i * batchSize, (i + 1) * batchSize)
      .map((app) => ({
        id: app.lms_id,
      }))

    const response = await LMS.getLoansByIds({ ids: loanIds })
    responses.push(response)
  }

  return responses.flat()
}

export async function getCredits(companyIds: string[]) {
  const uniqueIds = Array.from(new Set(companyIds))

  const responses: ICredit[][] = []
  const batchSize = 40
  const batchesCount = Math.ceil(uniqueIds.length / batchSize)

  for (let i = 0; i < batchesCount; i += 1) {
    const ids = uniqueIds.slice(i * batchSize, (i + 1) * batchSize)

    const response = await LMS.getCreditsByCompanyIds(ids)
    responses.push(response)
  }

  return new Map(
    responses.flat().map((credit) => [credit.companyId ?? '', credit]),
  )
}

export async function getDrawApprovals(apps: ILoanApplication[]) {
  const ids = apps.map((app) => app.drawApprovalId).filter((id) => !!id)
  const drawApprovals: IDrawApproval[] = []

  for (const id of ids) {
    const approval = await onBoardingService.getDrawApprovalById(id as string)
    drawApprovals.push(approval)
  }

  return drawApprovals
}

export async function getPaymentPlans(apps: ILoanApplication[]) {
  const paymentPlans = new Map<string, ILoanPaymentPlan | null>()

  const missedPlanNames = [
    ...new Set(
      apps
        .filter((app) => !app.metadata?.paymentPlan)
        .map((app) => app.invoiceDetails?.paymentPlan)
        .filter((plan) => !!plan),
    ),
  ]

  const plans = await Promise.all(missedPlanNames.map((name) => getPlan(name)))

  plans.forEach((plan, index) => paymentPlans.set(missedPlanNames[index], plan))

  return paymentPlans
}

export async function getPricingPackages(
  apps: (ILoanApplication & { supplier: ICompany })[],
) {
  const pricingPackages = new Map<string, ILoanPricingPackage | null>()

  const missedPackageNames = [
    ...new Set(
      apps
        .filter((app) => !app.metadata?.loanPackage)
        .map((app) => app.supplier?.settings.loanPricingPackageId)
        .filter((id) => !!id),
    ),
  ]

  const packages = await Promise.all(
    missedPackageNames.map(getLoanPricingPackage),
  )

  packages.forEach((pack, index) =>
    pricingPackages.set(<string>missedPackageNames[index], pack),
  )

  return pricingPackages
}

export function getReceivablesByType(
  receivables: IReceivable[],
  types: ReceivableType | ReceivableType[],
) {
  return receivables.filter((f) =>
    Array.isArray(types) ? types.includes(f.type) : types === f.type,
  )
}

export function getReceivablesCollectedTotal(
  receivables: IReceivable[],
  types: ReceivableType | ReceivableType[],
) {
  return sum(getReceivablesByType(receivables, types).map((f) => f.paidAmount))
}

export function getReceivablesOutstandingTotal(
  receivables: IReceivable[],
  types: ReceivableType | ReceivableType[],
) {
  return sum(
    getReceivablesByType(receivables, types).map((f) => f.expectedAmount),
  )
}

export function getReceivablesAssessedTotal(
  receivables: IReceivable[],
  types: ReceivableType | ReceivableType[],
) {
  return sum(
    getReceivablesByType(receivables, types).map(
      (f) => f.expectedAmount + f.adjustAmount,
    ),
  )
}
export function yesForRejected(knockoutScore: IScore | undefined) {
  return knockoutScore?.pass === ScoringResult.Reject ? 'Yes' : 'No'
}

export function formatNumericScore(knockoutScore: IScore | undefined) {
  const score = knockoutScore?.score
  return Array.isArray(score) ? score.join(', ') : score ?? ''
}

export function formatPercentage(amount: string | number | undefined) {
  return amount || amount === 0 ? `${amount}%` : ''
}

export function formatDate(date: string | Date | null | undefined) {
  // although dates are stored in CST by node.js backend, time zone info is lost in mongo, so force use UTC here to avoid double conversion
  return date ? moment.utc(date).format('MM/DD/YYYY') : ''
}

export function abbreviateState(draft: IUnifiedApplicationDraft | null) {
  return (
    statesHashMap[draft?.businessOwner_address?.state as States] ??
    draft?.businessOwner_address?.state
  )
}
