import { ControllerItem } from '../../controllerItem'
import { QuoteService } from '@linqpal/common-backend/src/services/quote/quote.service'
import { Logger } from '@linqpal/common-backend'

const logger = new Logger({
  module: 'postTransaction',
})

export const postTransaction: ControllerItem = {
  post: async (req, res) => {
    const { body, headers, user, session } = req
    const { quoteId, amount } = body

    logger.info(
      { quoteId, amount, userId: user.id },
      `Supplier posting transaction for quote ${quoteId}`,
    )

    if (!quoteId) throw new Error('quoteId is required')
    if (!amount) throw new Error('amount is required')

    await QuoteService.postTransaction(
      quoteId,
      amount,
      headers.origin ?? null,
      session,
    )

    res.send({ result: 'ok' })
  },
}
