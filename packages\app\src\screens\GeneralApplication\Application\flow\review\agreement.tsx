import React, { useEffect, useRef, useState } from 'react'
import ApplicationStore from '../../ApplicationStore'
import Loading from '../../../../Loading'
import { observer } from 'mobx-react'
import PdfViewer from '../../../../../ui/organisms/PdfViewer'
import { View } from 'react-native'
import { useTranslation } from 'react-i18next'
import { SubmitButton, VerifyEmailAlert } from '../../components'
import {
  fetchAgreement,
  resetAgreementPromise,
} from './requests/fetchAgreement'

function Agreement() {
  const { t } = useTranslation('application')
  const [fileUrl, setFileUrl] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [canSubmit, setCanSubmit] = useState(false)

  const {
    showVerifyEmail,
    setShowVerifyEmail,
    isGetPaidApplication,
    isCreditApplication,
  } = ApplicationStore

  const hasFetchedAgreement = useRef(false)

  useEffect(() => {
    if (hasFetchedAgreement.current) return

    const loadAgreement = async () => {
      setLoading(true)
      const url = await fetchAgreement()
      setFileUrl(url)
      setLoading(false)
      hasFetchedAgreement.current = true
    }
    loadAgreement()

    return () => {
      hasFetchedAgreement.current = false
      resetAgreementPromise()
    }
  }, [isGetPaidApplication, isCreditApplication])

  const handleReachedAgreementEnd = () => {
    setCanSubmit(true)
  }

  if (!(isGetPaidApplication || isCreditApplication)) return null
  if (loading) return <Loading />

  return fileUrl ? (
    <>
      <View style={{ alignItems: 'center' }}>
        <View style={{ marginTop: 20, marginBottom: 44 }}>
          <PdfViewer
            pdfUrl={fileUrl}
            onReachedAgreementEnd={handleReachedAgreementEnd}
          />
        </View>
        <SubmitButton t={t} canSubmit={canSubmit} />
      </View>
      <VerifyEmailAlert close={showVerifyEmail} setClose={setShowVerifyEmail} />
    </>
  ) : (
    // eslint-disable-next-line i18next/no-literal-string
    <View>{'Error loading document.'}</View>
  )
}

export default {
  title: 'Agreement.Heading',
  canMoveNext: false,
  canSkip: false,
  component: observer(Agreement),
  showFooterMessage: false,
}
